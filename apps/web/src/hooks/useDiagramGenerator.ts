/**
 * 架构图生成相关Hook
 * 使用 Zustand 状态管理，基于 LangChain Agent 的新架构
 * 重构后使用 diagram-core 包的业务逻辑函数
 */
import { toast } from 'react-hot-toast';
import type { DiagramGenerationRequest } from '../agents/DiagramAgent';
import { agentManager } from '@flowmind/diagram-core';
import type { AIModelConfig, DirectCallConfig, DiagramData } from '../shared/types';
import {
  useCurrentDiagram,
  useNaturalLanguageInput,
  useIsGenerating,
  useIsOptimizing,
  useAiResponse,
  useSelectedModel,
  useAvailableModels,
  useDirectCallConfig
} from '../stores/hooks';
import { useAppStore } from '../stores/appStore';
import {
  ensureAgentRegistered,
  generateDiagramCore,
  optimizeDiagramCore,
  validateConnectionCore,
  getProviderFromModel
} from '@flowmind/diagram-core';

export const useDiagramGenerator = () => {
  // 使用 Zustand hooks
  const currentDiagram = useCurrentDiagram();
  const naturalInput = useNaturalLanguageInput();
  const isGenerating = useIsGenerating();
  const isOptimizing = useIsOptimizing();
  const aiResponse = useAiResponse();
  const selectedModel = useSelectedModel();
  const availableModels = useAvailableModels();
  const directCallConfig = useDirectCallConfig();
  
  // 使用 Zustand actions
  const setCurrentDiagram = useAppStore(state => state.setCurrentDiagram);
  const setNaturalInput = useAppStore(state => state.setNaturalLanguageInput);
  const setIsGenerating = useAppStore(state => state.setIsGenerating);
  const setIsOptimizing = useAppStore(state => state.setIsOptimizing);
  const setAiResponse = useAppStore(state => state.setAiResponse);
  const setErrorMessage = useAppStore(state => state.setErrorMessage);

  const generateDiagram = async (description?: string) => {
    const input = description || naturalInput;
    if (!input.trim()) {
      toast.error('请输入需求描述');
      return;
    }

    setIsGenerating(true);
    setErrorMessage(null);

    try {
      // 使用 diagram-core 包的业务逻辑函数
      const result = await generateDiagramCore({
        input,
        selectedModel,
        availableModels,
        directCallConfig,
        currentDiagram,
        agentManager,
        ensureAgentRegistered,
        getProviderFromModel
      });

      // 更新状态
      setAiResponse(result.frontendResult);
      setCurrentDiagram({
        ...currentDiagram,
        description: input,
        mermaidCode: result.mermaidCode,
        diagramType: result.diagramType as DiagramData['diagramType']
      });

      const providerName = result.metadata.provider || 'AI';
      toast.success(`架构图生成成功！(使用 ${providerName})`);

    } catch (error) {
      console.error('Agent 图表生成失败:', error);
      setErrorMessage(error instanceof Error ? error.message : String(error));
      
      // 提供具体的错误建议
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('API') || errorMessage.includes('密钥')) {
        toast.error('API 密钥问题，请检查配置');
      } else if (errorMessage.includes('Agent')) {
        toast.error('Agent 初始化失败，请检查配置');
      } else {
        toast.error(errorMessage || '生成失败，请重试');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const optimizeDiagram = async (requirements: string) => {
    if (!requirements.trim()) {
      toast.error('请输入优化要求');
      return;
    }

    if (!currentDiagram.mermaidCode) {
      toast.error('没有可优化的图表代码');
      return;
    }

    setIsOptimizing(true);
    setErrorMessage(null);

    try {
      // 使用 diagram-core 包的业务逻辑函数
      const result = await optimizeDiagramCore({
        requirements,
        currentDiagram,
        selectedModel,
        availableModels,
        directCallConfig,
        agentManager,
        ensureAgentRegistered,
        getProviderFromModel
      });

      // 更新状态
      setAiResponse(result.frontendResult);
      setCurrentDiagram({
        ...currentDiagram,
        mermaidCode: result.mermaidCode,
        diagramType: result.diagramType as DiagramData['diagramType']
      });

      const providerName = result.metadata.provider || 'AI';
      toast.success(`图表优化成功！(使用 ${providerName})`);

    } catch (error) {
      console.error('Agent 图表优化失败:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      setErrorMessage(errorMessage);
      toast.error(errorMessage || '优化失败，请重试');
    } finally {
      setIsOptimizing(false);
    }
  };

  const validateConnection = async () => {
    try {
      // 使用 diagram-core 包的业务逻辑函数
      const result = await validateConnectionCore({
        selectedModel,
        availableModels,
        directCallConfig,
        agentManager
      });

      if (result.success) {
        toast.success(result.message);
        console.log('验证详情:', result.details);
      } else {
        toast.error(result.message);
        console.error('验证失败:', result.details);
      }

      return result;

    } catch (error) {
      console.error('连接验证异常:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      toast.error(errorMessage || '验证失败');
      return {
        success: false,
        message: errorMessage
      };
    }
  };

  const diagnoseConnection = async () => {
    console.log('=== Agent 连接诊断开始 ===');
    
    const modelInfo = availableModels.find(m => m.name === selectedModel);
    if (!modelInfo) {
      console.log('诊断结果: 未选择模型');
      return;
    }

    const providerConfig = directCallConfig[modelInfo.provider];
    console.log('诊断信息:');
    console.log('- 提供商:', modelInfo.provider);
    console.log('- 模型:', modelInfo.model);
    console.log('- API密钥状态:', providerConfig?.apiKey ? '已配置' : '未配置');
    console.log('- 已注册的 Agents:', agentManager.getAvailableAgents());

    if (providerConfig?.apiKey) {
      console.log('- API密钥预览:', `${providerConfig.apiKey.substring(0, 8)}...`);
      
      try {
        const validation = await validateConnection();
        console.log('- 连接测试结果:', validation.success ? '成功' : '失败');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.log('- 连接测试异常:', errorMessage);
      }
    }
  };

  const updateMermaidCode = (code: string) => {
    setCurrentDiagram({ ...currentDiagram, mermaidCode: code });
  };

  const resetDiagram = () => {
    setCurrentDiagram({
      title: '',
      mermaidCode: '',
      description: '',
      diagramType: 'flowchart',
      tags: []
    });
    setNaturalInput('');
    setAiResponse(null);
    setErrorMessage(null);
    agentManager.clearAllHistory();
  };

  return {
    // 状态
    currentDiagram,
    naturalInput,
    isGenerating,
    isOptimizing,
    aiResponse,
    
    // 操作
    generateDiagram,
    optimizeDiagram,
    updateMermaidCode,
    resetDiagram,
    setNaturalInput,
    
    // 工具
    validateConnection,
    diagnoseConnection
  };
};
